const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// ABSOLUTELY NO CSP HEADERS - EVER
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  
  // Remove any CSP headers that might exist
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('Content-Security-Policy-Report-Only');
  
  // Set permissive headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', '*');
  res.setHeader('Access-Control-Allow-Headers', '*');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  
  next();
});

// Serve static files from frontend/dist
const staticPath = path.join(__dirname, 'frontend', 'dist');
console.log(`Serving static files from: ${staticPath}`);

if (fs.existsSync(staticPath)) {
  app.use(express.static(staticPath, {
    setHeaders: (res, filePath) => {
      console.log(`Serving: ${filePath}`);
      // NEVER set CSP headers
      res.removeHeader('Content-Security-Policy');
      res.removeHeader('Content-Security-Policy-Report-Only');
    }
  }));
} else {
  console.error(`Static path does not exist: ${staticPath}`);
}

// API endpoints (mock)
app.post('/api/gemini', (req, res) => {
  res.json({ text: 'Mock Gemini response', success: true });
});

app.post('/api/openai', (req, res) => {
  res.json({ text: 'Mock OpenAI response', success: true });
});

app.post('/api/deepseek', (req, res) => {
  res.json({ text: 'Mock DeepSeek response', success: true });
});

// Catch-all handler for SPA
app.get('*', (req, res) => {
  const indexPath = path.join(staticPath, 'index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).send('Frontend not built. Run: cd frontend && npm run build');
  }
});

app.listen(PORT, () => {
  console.log(`Simple server running on port ${PORT}`);
  console.log(`Static files: ${staticPath}`);
  console.log(`Index exists: ${fs.existsSync(path.join(staticPath, 'index.html'))}`);
});
