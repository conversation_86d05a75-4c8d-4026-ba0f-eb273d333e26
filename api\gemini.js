// Vercel serverless function for Gemini API integration
const { GoogleGenerativeAI } = require('@google/generative-ai');

// Initialize the Gemini API with the API key from environment variables
// Normalize environment variables to handle different formats
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;
process.env.google_api_key = process.env.GEMINI_API_KEY || process.env.google_api_key;

const apiKey = process.env.GEMINI_API_KEY;
console.log('API Key status:', apiKey ? 'Key is available' : 'No key available');

// Don't initialize genAI here, we'll do it in the handler function to ensure we have the latest env vars

module.exports = async (req, res) => {
  console.log('Gemini serverless function called');
  console.log('Request method:', req.method);
  console.log('Request headers:', JSON.stringify(req.headers));
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    res.status(204).end();
    return;
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    console.log('Request body:', JSON.stringify(req.body));
    
    // Get the prompt/question and model from the request body
    // Support both 'prompt' and 'question' parameter names for flexibility
    const { prompt, question, model: selectedModel = 'gemini-1.5-pro', thinkingMode = false } = req.body || {};
    
    // Use prompt if available, otherwise fall back to question
    const promptText = prompt || question;
    
    if (!promptText) {
      return res.status(400).json({ error: 'Prompt or question is required' });
    }
    
    console.log('Processing prompt:', promptText);
    console.log('Selected model:', selectedModel);
    console.log('Thinking mode:', thinkingMode ? 'Enabled' : 'Disabled');
    
    // Get API key from environment variables
    // Ensure we're using the normalized environment variables
    process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || process.env.google_api_key;
    process.env.google_api_key = process.env.GEMINI_API_KEY || process.env.google_api_key;
    
    const apiKey = process.env.GEMINI_API_KEY;
    
    if (!apiKey) {
      console.error('No API key available. Please set GEMINI_API_KEY or google_api_key in your environment variables.');
      return res.status(500).json({ error: 'API key not configured. Please check server configuration.' });
    }
    
    console.log('Using API key from environment variables');
    
    // Initialize the Gemini API
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: selectedModel });
    
    // Prepare the final prompt based on thinking mode
    let finalPrompt = '';
    if (thinkingMode) {
      finalPrompt = `You are a helpful AI assistant specializing in marketing and client acquisition strategies.
      When answering, please follow this structure:
      1. THINKING: First, think step by step about the question. Consider different angles, relevant marketing concepts, and potential strategies. This section is for your analytical process.
      2. ANSWER: Then provide your final, well-structured answer based on your thinking.
      
      Question: ${promptText}`;
    } else {
      // If the prompt already includes system instructions, use it directly
      if (promptText.includes('You are') || promptText.length > 500) {
        finalPrompt = promptText;
      } else {
        finalPrompt = `You are a helpful AI assistant specializing in marketing and client acquisition strategies.
        Please provide a helpful response to the following question: ${promptText}`;
      }
    }
    
    // Generate content using the Gemini model
    console.log('Sending prompt to Gemini API:', finalPrompt.substring(0, 100) + '...');
    const result = await model.generateContent(finalPrompt);
    
    console.log('Raw result:', JSON.stringify(result));
    
    // Extract the text from the response
    let responseText;
    if (result.response && typeof result.response.text === 'function') {
      responseText = result.response.text();
      console.log('Response text extracted from function call');
    } else if (result.response && result.response.text) {
      responseText = result.response.text;
      console.log('Response text extracted from property');
    } else if (result.text) {
      responseText = result.text;
      console.log('Response text extracted from result.text');
    } else {
      responseText = 'No text found in response';
      console.log('No response text found in any expected location');
    }
    
    // Return the successful response
    return res.status(200).json({
      text: responseText,
      model: selectedModel,
      success: true
    });
    
  } catch (error) {
    console.error('Error processing Gemini request:', error);
    console.error('Error stack:', error.stack);
    
    // Check for rate limit errors
    if (error.message && (error.message.includes('quota') || error.message.includes('rate limit'))) {
      console.log('Rate limit error detected');
      return res.status(429).json({ 
        error: 'API rate limit exceeded. Please try again later.', 
        details: error.message,
        isRateLimit: true
      });
    }
    
    // Check for API key errors
    if (error.message && error.message.includes('API key not valid')) {
      console.log('Invalid API key error detected');
      return res.status(401).json({ 
        error: 'Invalid API key. Please check your GEMINI_API_KEY environment variable.', 
        details: 'The API key provided was rejected by the Gemini API service.', 
        troubleshooting: [
          'Verify that your API key is correctly copied from the Google AI Studio (https://makersuite.google.com/)',
          'Ensure there are no extra spaces or characters in your API key',
          'Check if your API key has been revoked or expired',
          'Confirm that your API key has access to the requested model (gemini-1.5-pro)',
          'Try generating a new API key from the Google AI Studio',
          'Make sure your .env file is correctly formatted (GEMINI_API_KEY=your_key_here without quotes)'
        ]
      });
    }
    
    // Other errors
    return res.status(500).json({ 
      error: error.message || 'Failed to generate response',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};
