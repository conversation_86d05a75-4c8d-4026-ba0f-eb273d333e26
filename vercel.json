{"rewrites": [{"source": "/api/questionnaire/:action/:id", "destination": "/api/questionnaire.py?action=:action&id=:id"}, {"source": "/api/questionnaire/:action", "destination": "/api/questionnaire.py?action=:action"}, {"source": "/api/responses/:action/:id", "destination": "/api/responses.py?action=:action&id=:id"}, {"source": "/api/responses/:action", "destination": "/api/responses.py?action=:action"}, {"source": "/api/gemini", "destination": "/api/gemini.js"}, {"source": "/api/openai", "destination": "/api/openai.js"}, {"source": "/api/deepseek", "destination": "/api/deepseek.js"}, {"source": "/api/save-document", "destination": "/api/save-document.js"}, {"source": "/api/yaml", "destination": "/api/index.py"}, {"source": "/api", "destination": "/api/index.py"}, {"source": "/api/(.*)", "destination": "/api/index.py"}, {"source": "/(.*)", "destination": "/"}], "env": {"PYTHONPATH": "/var/task"}}