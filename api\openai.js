// Vercel serverless function for OpenAI API integration
console.log('Loading OpenAI module...');
const { OpenAI } = require('openai');
console.log('OpenAI module loaded successfully');

module.exports = async (req, res) => {
  console.log('OpenAI serverless function called');
  console.log('Request method:', req.method);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle OPTIONS requests (preflight)
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    res.status(204).end();
    return;
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    console.log('Request body:', JSON.stringify(req.body));
    
    const { question, model = 'gpt-4o', thinkingMode = false } = req.body || {};
    
    if (!question) {
      return res.status(400).json({ error: 'Question is required' });
    }
    
    console.log('Processing question:', question);
    console.log('Selected model:', model);
    console.log('Thinking mode:', thinkingMode ? 'Enabled' : 'Disabled');
    
    // Get API key from environment variables
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (!apiKey) {
      console.log('API Key status: No key available');
      return res.status(500).json({ error: 'OpenAI API key not configured' });
    }
    
    console.log('API Key status: Key is available');
    
    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: apiKey
    });
    
    // Construct the prompt based on thinking mode
    let prompt = '';
    if (thinkingMode) {
      prompt = `I want you to answer the following question about market research and client acquisition. First, show your thinking process labeled as "THINKING:" where you work through the question step by step. Then provide a clear, concise answer labeled as "ANSWER:".

Question: ${question}`;
    } else {
      prompt = `Answer the following question about market research and client acquisition with a clear, structured response:

Question: ${question}`;
    }
    
    console.log('Sending request to OpenAI API...');
    
    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        { role: "system", content: "You are a helpful market research and client acquisition expert. Provide detailed, actionable advice with examples when possible." },
        { role: "user", content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });
    
    // Extract the response text
    const responseText = completion.choices[0].message.content;
    console.log('Response received from OpenAI');
    
    return res.status(200).json({ 
      answer: responseText, 
      model: model,
      thinkingMode: thinkingMode
    });
    
  } catch (error) {
    console.error('Error processing OpenAI request:', error);
    
    // Handle rate limiting errors
    if (error.status === 429) {
      return res.status(429).json({ 
        error: 'OpenAI API rate limit exceeded. Please try again later.',
        fallback: true
      });
    }
    
    // Handle invalid API key errors
    if (error.status === 401) {
      console.log('Invalid OpenAI API key error detected');
      return res.status(401).json({ 
        error: 'Invalid OpenAI API key. Please check your OPENAI_API_KEY environment variable.', 
        details: 'The API key provided was rejected by the OpenAI API service.',
        troubleshooting: [
          'Verify that your API key is correctly copied from the OpenAI dashboard (https://platform.openai.com/api-keys)',
          'Ensure there are no extra spaces or characters in your API key',
          'Check if your API key has been revoked or expired',
          'Confirm that your API key has access to the requested model (' + model + ')',
          'Try generating a new API key from the OpenAI dashboard',
          'Make sure your .env file is correctly formatted (OPENAI_API_KEY=your_key_here without quotes)',
          'Verify that your OpenAI account has a valid payment method if using paid models'
        ],
        fallback: true
      });
    }
    
    return res.status(500).json({ 
      error: error.message || 'Internal server error',
      fallback: true
    });
  }
};
