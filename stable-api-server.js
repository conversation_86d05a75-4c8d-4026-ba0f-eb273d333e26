// Simplified and stable API server for the Market Research Tool
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Create Express app immediately
const app = express();
const PORT = process.env.PORT || 3001; // Use PORT from environment or default to 3001

// Handle favicon.ico requests with no-cache headers
app.get('/favicon.ico', (req, res) => {
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.status(204).end();
});

// CRITICAL: Add healthcheck endpoints before anything else
// This ensures they'll work even if other parts of the app have issues
app.get('/api/healthcheck', (req, res) => {
  console.log('Healthcheck endpoint called at:', new Date().toISOString());
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

app.get('/api', (req, res) => {
  console.log('Root API endpoint called at:', new Date().toISOString());
  res.status(200).json({ status: 'ok' });
});

// Add a diagnostic endpoint to help troubleshoot the Railway deployment
app.get('/api/diagnostic', (req, res) => {
  console.log('Diagnostic endpoint called at:', new Date().toISOString());

  // Check if frontend build exists
  const frontendExists = fs.existsSync(path.join(__dirname, 'frontend', 'dist', 'index.html'));
  const frontendPath = path.join(__dirname, 'frontend', 'dist');
  let frontendFiles = [];

  try {
    if (fs.existsSync(frontendPath)) {
      frontendFiles = fs.readdirSync(frontendPath);
    }
  } catch (err) {
    console.error('Error reading frontend directory:', err);
  }
  
  // Collect system information
  const diagnosticInfo = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'not set',
    platform: process.platform,
    nodeVersion: process.version,
    memoryUsage: process.memoryUsage(),
    uptime: process.uptime(),
    dirname: __dirname,
    cwd: process.cwd(),
    frontend: {
      buildExists: frontendExists,
      buildPath: frontendPath,
      files: frontendFiles,
      indexPath: indexPath || 'Not found',
      frontendBuildPath: frontendBuildPath || 'Not found'
    },
    env: {
      PORT: process.env.PORT,
      // Include other non-sensitive environment variables
      RAILWAY_STATIC_URL: process.env.RAILWAY_STATIC_URL,
      RAILWAY_SERVICE_ID: process.env.RAILWAY_SERVICE_ID,
      RAILWAY_PROJECT_ID: process.env.RAILWAY_PROJECT_ID
    },
    paths: {
      checked: possiblePaths.map(path => ({
        path,
        exists: fs.existsSync(path),
        isDirectory: fs.existsSync(path) ? fs.statSync(path).isDirectory() : false
      }))
    },
    routes: app._router.stack
      .filter(r => r.route)
      .map(r => ({
        path: r.route.path,
        methods: Object.keys(r.route.methods).filter(m => r.route.methods[m])
      }))
  };
  
  // Return the diagnostic information
  res.status(200).json(diagnosticInfo);
});

// Ensure the api-status endpoint is defined before any catch-all routes
app.get('/api-status', (req, res) => {
  console.log('API status endpoint called at:', new Date().toISOString());
  res.send(`
    <html>
      <head>
        <title>Market Research Tool - API Status</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .warning { color: orange; }
          .error { color: red; }
          a { color: #0066cc; text-decoration: none; }
          a:hover { text-decoration: underline; }
        </style>
      </head>
      <body>
        <h1>Market Research Tool - API Status</h1>
        <div class="card">
          <h2>Server Status</h2>
          <p class="success">✅ API Server is running on port ${PORT}</p>
        </div>
        
        <div class="card">
          <h2>Available API Endpoints</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
            <li><strong>API Status:</strong> GET /api</li>
            <li><strong>Health Check:</strong> GET /api/healthcheck</li>
            <li><strong>Railway Health:</strong> GET /api/railway-health</li>
            <li><strong>Diagnostic:</strong> GET <a href="/api/diagnostic">/api/diagnostic</a></li>
          </ul>
        </div>
        
        <div class="card">
          <h2>Troubleshooting</h2>
          <p>If you're experiencing issues with the frontend, check the <a href="/api/diagnostic">diagnostic endpoint</a> for detailed information about the server environment.</p>
        </div>

        <div class="card">
          <h2>Environment Information</h2>
          <pre>${JSON.stringify({
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'not set',
            platform: process.platform,
            nodeVersion: process.version,
            uptime: process.uptime(),
            dirname: __dirname,
            cwd: process.cwd(),
            frontendBuildPath: frontendBuildPath || 'Not found',
            indexPath: indexPath || 'Not found'
          }, null, 2)}</pre>
        </div>
      </body>
    </html>
  `);
});

// Add a dedicated Railway healthcheck endpoint that won't interfere with the frontend
app.get('/api/railway-health', (req, res) => {
  console.log('Railway healthcheck endpoint called at:', new Date().toISOString());
  res.status(200).json({ status: 'ok' });
});

console.log('Healthcheck endpoints registered at: /api/railway-health, /api/healthcheck and /api');

// Load environment variables with explicit path
console.log('Loading environment variables...');
try {
  const envPath = path.resolve(__dirname, '.env');
  console.log(`Loading .env file from: ${envPath}`);
  
  // Check if .env file exists and is readable
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    console.log('First few characters of .env file:', envContent.substring(0, 20).replace(/\n/g, '\\n'));
    
    const result = dotenv.config({ path: envPath });
    
    if (result.error) {
      console.error('Error loading .env file:', result.error);
    } else {
      console.log('.env file loaded successfully');
    }
  } else {
    console.error('.env file not found at path:', envPath);
  }
} catch (error) {
  console.error('Error during .env loading:', error);
}

// Normalize environment variables to handle different formats
// This ensures we can handle both uppercase and lowercase variants
function normalizeEnvVars() {
  // Gemini API key
  const geminiKey = process.env.GEMINI_API_KEY || process.env.google_api_key;
  if (geminiKey) {
    process.env.GEMINI_API_KEY = geminiKey;
    process.env.google_api_key = geminiKey;
  }
  
  // OpenAI API key
  const openaiKey = process.env.OPENAI_API_KEY || process.env.openai_api_key;
  if (openaiKey) {
    process.env.OPENAI_API_KEY = openaiKey;
    process.env.openai_api_key = openaiKey;
  }
  
  // DeepSeek API key
  const deepseekKey = process.env.DEEPSEEK_API_KEY || process.env.deepseek_api_key;
  if (deepseekKey) {
    process.env.DEEPSEEK_API_KEY = deepseekKey;
    process.env.deepseek_api_key = deepseekKey;
  }
  
  // Anthropic API key (if used)
  const anthropicKey = process.env.ANTHROPIC_API_KEY || process.env.anthropic_api_key;
  if (anthropicKey) {
    process.env.ANTHROPIC_API_KEY = anthropicKey;
    process.env.anthropic_api_key = anthropicKey;
  }
  
  // Groq API key (if used)
  const groqKey = process.env.GROQ_API_KEY || process.env.groq_api_key;
  if (groqKey) {
    process.env.GROQ_API_KEY = groqKey;
    process.env.groq_api_key = groqKey;
  }
}

// Run the normalization
normalizeEnvVars();

// Log environment variables to verify they're loaded (without showing full keys)
console.log('Environment variables loaded:');
function logKeyStatus(keyName) {
  const key = process.env[keyName];
  if (key) {
    const firstChars = key.substring(0, 5);
    const lastChars = key.substring(key.length - 3);
    console.log(`- ${keyName}: Available (${firstChars}...${lastChars})`);
  } else {
    console.log(`- ${keyName}: Not available`);
  }
}

logKeyStatus('GEMINI_API_KEY');
logKeyStatus('OPENAI_API_KEY');
logKeyStatus('DEEPSEEK_API_KEY');
logKeyStatus('ANTHROPIC_API_KEY');
logKeyStatus('GROQ_API_KEY');

// Middleware setup

// Middleware with extremely permissive CORS settings
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Global middleware to set permissive security headers
app.use((req, res, next) => {
  // Remove all CSP headers completely
  res.removeHeader('Content-Security-Policy');
  res.removeHeader('Content-Security-Policy-Report-Only');
  
  // Add permissive CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  
  // Add security headers with permissive values
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none');
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none');
  
  // We no longer need to override setHeader since we're setting our own CSP
  // Instead of preventing CSP headers, we'll just let our permissive one take effect
  
  next();
});

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Add an alternative route to bypass caching
app.get('/nocache', (req, res) => {
  // Set aggressive no-cache headers
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('Surrogate-Control', 'no-store');
  
  // Serve the React app with a timestamp to ensure uniqueness
  const timestamp = new Date().toISOString();
  res.send(`
    <html>
      <head>
        <title>Market Research Tool (No Cache) - ${timestamp}</title>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <script>
          // Redirect to the main app with cache-busting parameter
          window.location.href = '/?nocache=' + Date.now();
        </script>
      </head>
      <body>
        <p>Redirecting to no-cache version...</p>
      </body>
    </html>
  `);
});

app.use((req, res, next) => {
  express.json()(req, res, (err) => {
    if (err) {
      console.error('Invalid JSON in request body:', err.message);
      return res.status(400).json({ error: 'Invalid JSON in request body' });
    }
    next();
  });
});

// API status endpoint
app.get('/api', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Market Research API Server</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .error { color: red; }
        </style>
      </head>
      <body>
        <h1>Market Research API Server</h1>
        <p>The API server is running successfully on port ${PORT}</p>
        
        <div class="endpoint">
          <h2>Available Endpoints:</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
          </ul>
        </div>
        
        <div class="endpoint">
          <h2>Environment Status:</h2>
          <ul>
            <li><strong>Gemini API Key:</strong> <span class="${process.env.GEMINI_API_KEY ? 'success' : 'error'}">${process.env.GEMINI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>OpenAI API Key:</strong> <span class="${process.env.OPENAI_API_KEY ? 'success' : 'error'}">${process.env.OPENAI_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>DeepSeek API Key:</strong> <span class="${process.env.DEEPSEEK_API_KEY ? 'success' : 'error'}">${process.env.DEEPSEEK_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>Anthropic API Key:</strong> <span class="${process.env.ANTHROPIC_API_KEY ? 'success' : 'error'}">${process.env.ANTHROPIC_API_KEY ? 'Available' : 'Not Available'}</span></li>
            <li><strong>Groq API Key:</strong> <span class="${process.env.GROQ_API_KEY ? 'success' : 'error'}">${process.env.GROQ_API_KEY ? 'Available' : 'Not Available'}</span></li>
          </ul>
        </div>
      </body>
    </html>
  `);
});

// Simple mock handlers for all API endpoints
function createMockHandler(modelName) {
  return (req, res) => {
    try {
      console.log(`${modelName} API endpoint called`);
      console.log('Request body:', req.body);
      
      const prompt = req.body.prompt || req.body.question || 'No prompt provided';
      const model = req.body.model || `${modelName.toLowerCase()}-default`;
      
      console.log(`Processing ${modelName} request with model: ${model}`);
      
      // Return a mock response
      return res.status(200).json({
        text: `This is a mock response from the ${modelName} API using the ${model} model.\n\nYour prompt was: "${prompt}"`,
        model: model,
        success: true
      });
    } catch (error) {
      console.error(`Error in ${modelName} handler:`, error);
      return res.status(500).json({
        error: 'Internal server error',
        details: error.message
      });
    }
  };
}

// API routes with mock handlers
app.post('/api/gemini', createMockHandler('Gemini'));
app.post('/api/openai', createMockHandler('OpenAI'));
app.post('/api/deepseek', createMockHandler('DeepSeek'));
app.post('/api/anthropic', createMockHandler('Anthropic'));
app.post('/api/groq', createMockHandler('Groq'));

// Global error handler - must be added after all routes
app.use((err, req, res, next) => {
  console.error('Unhandled error in request:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'The server encountered an unexpected error. Please try again later.',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Process-level error handling to prevent crashes
// Note: uncaughtException is now handled in the path-to-regexp patch above

process.on('unhandledRejection', (err) => {
  console.error('UNHANDLED REJECTION!');
  console.error(err.name, err.message, err.stack);
  // Log the error but keep the server running
});

// Keep the Node.js process alive even if there are errors
setInterval(() => {
  console.log('Server heartbeat check - still running at ' + new Date().toISOString());
}, 30000); // Log every 30 seconds to show the server is still alive

// Create a file to indicate the server is running
fs.writeFileSync(path.join(__dirname, 'server-running.txt'), 'Server started at ' + new Date().toISOString());

// Update the file periodically to show the server is still running
setInterval(() => {
  fs.writeFileSync(path.join(__dirname, 'server-running.txt'), 'Server running at ' + new Date().toISOString());
}, 10000); // Update every 10 seconds

// Patch for path-to-regexp error that occurs with certain versions of react-router-dom
// This prevents the "Missing parameter name at X: https://git.new/pathToRegexpError" error
try {
  // Only apply this patch if the path-to-regexp module is available
  const pathToRegexpPath = require.resolve('path-to-regexp');
  if (pathToRegexpPath) {
    console.log('Applying path-to-regexp patch to prevent router errors');
    // We're adding a global try-catch to prevent the server from crashing
    // due to path-to-regexp errors with certain URLs
    process.on('uncaughtException', (err) => {
      if (err.message && err.message.includes('pathToRegexpError')) {
        console.error('Caught path-to-regexp error (non-fatal):', err.message);
        // Continue execution - this error won't crash the server
      } else {
        // For other uncaught exceptions, log them but don't crash
        console.error('UNCAUGHT EXCEPTION (non-fatal):', err.name, err.message);
        console.error(err.stack);
      }
    });
  }
} catch (error) {
  console.log('path-to-regexp module not found, skipping patch');
}

// Serve static files
// Check for the frontend build directory
let frontendBuildPath;
let indexPath;

// Get Railway specific paths if available
const railwayStaticUrl = process.env.RAILWAY_STATIC_URL;
const railwayVolumePath = process.env.RAILWAY_VOLUME_MOUNT_PATH;

const possiblePaths = [
  // Railway-specific paths
  railwayVolumePath ? path.join(railwayVolumePath, 'frontend/dist') : null,
  railwayVolumePath ? path.join(railwayVolumePath, 'dist') : null,
  railwayVolumePath ? path.join(railwayVolumePath, 'build') : null,
  '/app/frontend/dist',  // Common Railway path
  '/app/dist',           // Common Railway path
  '/app/build',          // Common Railway path
  '/app',                // Root app directory
  
  // Standard paths
  path.join(__dirname, 'frontend', 'dist'),
  path.join(__dirname, 'dist'),
  path.join(__dirname, 'build'),
  path.resolve(__dirname, 'client/build'),
  path.resolve(__dirname, '../client/build'),
  path.resolve(__dirname, './build'),
  path.resolve(__dirname, './client/build'),
  path.resolve(__dirname, './frontend/build'),
  path.resolve(__dirname, './frontend/dist'),
  
  // Current working directory paths
  path.join(process.cwd(), 'frontend', 'dist'),
  path.join(process.cwd(), 'client', 'build'),
  path.join(process.cwd(), 'build'),
  path.join(process.cwd(), 'dist'),
  
  // Other cloud provider paths
  '/opt/render/project/src/frontend/dist',
  '/opt/render/project/src/dist',
  '/opt/render/project/src/build'
].filter(Boolean); // Remove null entries

// Find the frontend build directory and index.html
for (const p of possiblePaths) {
  if (fs.existsSync(p)) {
    try {
      const stat = fs.statSync(p);
      if (stat.isDirectory()) {
        frontendBuildPath = p;
        console.log(`Found frontend build directory at: ${frontendBuildPath}`);
        
        // Check for index.html in the build directory
        const indexInBuild = path.join(frontendBuildPath, 'index.html');
        if (fs.existsSync(indexInBuild)) {
          indexPath = indexInBuild;
          console.log(`Found index.html in build directory: ${indexPath}`);
          break;
        }
      }
    } catch (err) {
      console.error(`Error checking path ${p}:`, err);
    }
  }
}

// If we found the build directory but not index.html, look for it in subdirectories
if (frontendBuildPath && !indexPath) {
  console.log('Looking for index.html in subdirectories...');
  
  // Check for index.html in common subdirectories
  const possibleIndexPaths = [
    path.join(frontendBuildPath, 'index.html'),
    path.join(frontendBuildPath, 'public', 'index.html'),
    path.join(frontendBuildPath, 'static', 'index.html'),
    path.join(frontendBuildPath, '..', 'index.html'),
    path.join(__dirname, 'index.html'),
    path.join(process.cwd(), 'index.html'),
    // Check parent directories
    path.join(frontendBuildPath, '..', 'index.html'),
    path.join(frontendBuildPath, '..', '..', 'index.html'),
    // Check for index.html in the app root on Railway
    '/app/index.html'
  ];
  
  // Check each possible path
  for (const possiblePath of possibleIndexPaths) {
    if (fs.existsSync(possiblePath)) {
      indexPath = possiblePath;
      console.log(`Found index.html at: ${indexPath}`);
      break;
    } else {
      console.log(`index.html NOT found at: ${possiblePath}`);
    }
  }
  
  // If we still don't have index.html, create a fallback one
  if (!indexPath && frontendBuildPath) {
    console.log('Creating fallback index.html in the build directory');
    const fallbackIndexPath = path.join(frontendBuildPath, 'index.html');
    
    try {
      // Create a simple index.html that loads assets correctly
      const fallbackHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Market Research Tool</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
    #root { height: 100vh; }
  </style>
  <script>
    // This helps debug asset loading issues
    window.addEventListener('error', function(e) {
      console.error('Resource loading error:', e.target.src || e.target.href);
    }, true);
  </script>
</head>
<body>
  <div id="root"></div>
  <script>
    // Dynamically load the main JS file
    fetch('/assets')
      .then(response => response.text())
      .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = doc.querySelectorAll('a');
        const jsFiles = Array.from(links)
          .map(link => link.href)
          .filter(href => href.endsWith('.js'));
        
        if (jsFiles.length > 0) {
          const script = document.createElement('script');
          script.src = jsFiles[0].split('/').pop();
          script.type = 'module';
          document.body.appendChild(script);
          console.log('Loaded JS file:', script.src);
        }
      })
      .catch(err => console.error('Error loading assets:', err));
  </script>
</body>
</html>`;
      
      fs.writeFileSync(fallbackIndexPath, fallbackHtml);
      indexPath = fallbackIndexPath;
      console.log(`Created fallback index.html at: ${indexPath}`);
    } catch (err) {
      console.error('Error creating fallback index.html:', err);
    }
  }
}

// If we still haven't found index.html, check if it's in the root directory
if (!indexPath) {
  const rootIndexPath = path.join(__dirname, 'index.html');
  if (fs.existsSync(rootIndexPath)) {
    indexPath = rootIndexPath;
    console.log(`Found index.html in root directory: ${indexPath}`);
  }
}

// Define a function to render the API status page
const renderApiStatusPage = (req, res) => {
  console.log('API status page called at:', new Date().toISOString());
  res.send(`
    <html>
      <head>
        <title>Market Research Tool - API Status</title>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .warning { color: orange; }
          .error { color: red; }
          a { color: #0066cc; text-decoration: none; }
          a:hover { text-decoration: underline; }
        </style>
      </head>
      <body>
        <h1>Market Research Tool - API Status</h1>
        <div class="card">
          <h2>Server Status</h2>
          <p class="success">✅ API Server is running on port ${PORT}</p>
        </div>
        
        <div class="card">
          <h2>Available API Endpoints</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
            <li><strong>API Status:</strong> GET /api</li>
            <li><strong>Health Check:</strong> GET /api/healthcheck</li>
            <li><strong>Railway Health:</strong> GET /api/railway-health</li>
            <li><strong>Diagnostic:</strong> GET <a href="/api/diagnostic">/api/diagnostic</a></li>
          </ul>
        </div>
        
        <div class="card">
          <h2>Frontend Status</h2>
          <p class="${indexPath ? 'success' : 'error'}">Frontend index.html: ${indexPath ? '✅ Found at ' + indexPath : '❌ Not found'}</p>
          <p class="${frontendBuildPath ? 'success' : 'error'}">Frontend build directory: ${frontendBuildPath ? '✅ Found at ' + frontendBuildPath : '❌ Not found'}</p>
        </div>

        <div class="card">
          <h2>Environment Information</h2>
          <pre>${JSON.stringify({
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'not set',
            platform: process.platform,
            nodeVersion: process.version,
            uptime: process.uptime(),
            dirname: __dirname,
            cwd: process.cwd(),
            frontendBuildPath: frontendBuildPath || 'Not found',
            indexPath: indexPath || 'Not found',
            possiblePaths: possiblePaths.map(path => ({
              path,
              exists: fs.existsSync(path)
            }))
          }, null, 2)}</pre>
        </div>
      </body>
    </html>
  `);
};

// Use the API status page for the /api-status route
app.get('/api-status', renderApiStatusPage);

// Serve static files if the frontend build directory exists
if (frontendBuildPath && fs.existsSync(frontendBuildPath)) {
  console.log(`Serving frontend from: ${frontendBuildPath}`);
  
  // Log the contents of the frontend build directory
  try {
    const files = fs.readdirSync(frontendBuildPath);
    console.log(`Frontend build directory contents: ${files.join(', ')}`);
    
    // Check for subdirectories and log their contents too
    files.forEach(file => {
      const filePath = path.join(frontendBuildPath, file);
      if (fs.statSync(filePath).isDirectory()) {
        try {
          const subFiles = fs.readdirSync(filePath);
          console.log(`Subdirectory ${file} contents: ${subFiles.join(', ')}`);
        } catch (err) {
          console.error(`Error reading subdirectory ${file}:`, err);
        }
      }
    });
  } catch (err) {
    console.error('Error reading frontend build directory:', err);
  }
  
  // Look for assets directory specifically (common in Vite builds)
  const assetsDir = path.join(frontendBuildPath, 'assets');
  if (fs.existsSync(assetsDir)) {
    console.log(`Found assets directory at: ${assetsDir}`);
    try {
      const assetFiles = fs.readdirSync(assetsDir);
      console.log(`Assets directory contents: ${assetFiles.join(', ')}`);
    } catch (err) {
      console.error('Error reading assets directory:', err);
    }
  }
  
  // Serve static files with permissive CORS
  console.log('Setting up static file serving middleware');
  
  // First, try to serve from the root of the build directory
  app.use(express.static(frontendBuildPath, {
    setHeaders: (res, filePath) => {
      console.log(`Serving static file: ${filePath}`);
      // Set cache control headers
      if (filePath.endsWith('.html')) {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
      } else if (filePath.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
        res.setHeader('Cache-Control', 'public, max-age=86400');
      }
      
      // Set permissive CORS headers
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    }
  }));
  
  // Explicitly serve the assets directory if it exists
  if (fs.existsSync(assetsDir)) {
    console.log('Setting up explicit assets directory middleware');
    app.use('/assets', express.static(assetsDir, {
      setHeaders: (res, filePath) => {
        console.log(`Serving asset file: ${filePath}`);
        res.setHeader('Cache-Control', 'public, max-age=86400');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
      }
    }));
  }
  
  // If there's a public directory inside the build directory, serve it too
  const publicDir = path.join(frontendBuildPath, 'public');
  if (fs.existsSync(publicDir)) {
    console.log(`Found public directory at: ${publicDir}`);
    app.use('/public', express.static(publicDir, {
      setHeaders: (res) => {
        res.setHeader('Cache-Control', 'public, max-age=86400');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
      }
    }));
  }
  
  // If there's a static directory inside the build directory, serve it too
  const staticDir = path.join(frontendBuildPath, 'static');
  if (fs.existsSync(staticDir)) {
    console.log(`Found static directory at: ${staticDir}`);
    app.use('/static', express.static(staticDir, {
      setHeaders: (res) => {
        res.setHeader('Cache-Control', 'public, max-age=86400');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
      }
    }));
  }
  
  // API 404 handler for unknown API routes
  app.all('/api/*', (req, res, next) => {
    // Skip routes that are explicitly defined
    const stack = app._router.stack;
    const route = stack.find(layer => {
      if (!layer.route) return false;
      return layer.route.path === req.path && layer.route.methods[req.method.toLowerCase()];
    });
    
    if (route) {
      return next();
    }
    
    console.log(`404 for API route: ${req.path}`);
    return res.status(404).json({ error: 'API endpoint not found' });
  });
  
  // Serve index.html for all non-API routes
  app.get('*', (req, res, next) => {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return next();
    }
    
    console.log(`Serving index.html for path: ${req.path}`);
    
    if (indexPath && fs.existsSync(indexPath)) {
      console.log(`Sending index.html from: ${indexPath}`);
      
      // Set headers before sending file
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Access-Control-Allow-Origin': '*',
        'Cross-Origin-Resource-Policy': 'cross-origin',
        'Content-Type': 'text/html'
      });
      
      // Send the file with absolute path to avoid path resolution issues
      res.sendFile(path.resolve(indexPath), err => {
        if (err) {
          console.error(`Error sending index.html: ${err.message}`);
          next(err);
        }
      });
    } else {
      console.error('index.html not found, cannot serve frontend');
      // Serve a basic HTML page with diagnostic info
      res.status(404).set({
        'Content-Type': 'text/html',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*'
      }).send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Market Research Tool - Not Found</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
              h1 { color: #d32f2f; }
              .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
              pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
              a { color: #0066cc; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
          </head>
          <body>
            <h1>Index.html Not Found</h1>
            <div class="card">
              <h2>Debug Information</h2>
              <p>Could not find index.html at: ${indexPath || 'No path found'}</p>
              <p>Frontend build path: ${frontendBuildPath || 'Not found'}</p>
              <p>Current directory: ${__dirname}</p>
              <p>Try accessing the <a href="/api">/api</a> or <a href="/api-status">/api-status</a> endpoints instead.</p>
            </div>
          </body>
        </html>
      `);
    }
  });
} else {
  console.log('Frontend build directory not found, setting up fallback routes');
  
  // Create a function to render the API status page
  const renderApiStatusPage = (req, res) => {
    console.log('Rendering API status page at:', new Date().toISOString());
    res.send(`
      <html>
        <head>
          <title>Market Research Tool - API Status</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #333; }
            .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
            .success { color: green; }
            .warning { color: orange; }
            .error { color: red; }
            a { color: #0066cc; text-decoration: none; }
            a:hover { text-decoration: underline; }
          </style>
        </head>
        <body>
          <h1>Market Research Tool - API Status</h1>
          <div class="card">
            <h2>Server Status</h2>
            <p class="success">✅ API Server is running on port ${PORT}</p>
          </div>
          
          <div class="card">
            <h2>Available API Endpoints</h2>
            <ul>
              <li><strong>Gemini API:</strong> POST /api/gemini</li>
              <li><strong>OpenAI API:</strong> POST /api/openai</li>
              <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
              <li><strong>API Status:</strong> GET /api</li>
              <li><strong>Health Check:</strong> GET /api/healthcheck</li>
              <li><strong>Diagnostic:</strong> GET <a href="/api/diagnostic">/api/diagnostic</a></li>
            </ul>
          </div>
          
          <div class="card">
            <h2>Frontend Status</h2>
            <p class="warning">⚠️ Frontend build not found</p>
            <p>The API server is running, but the frontend build directory could not be found.</p>
            <p>Checked paths:</p>
            <ul>
              ${possiblePaths.map(p => `<li>${p}: ${fs.existsSync(p) ? '✅ exists' : '❌ not found'}</li>`).join('')}
            </ul>
          </div>
          
          <div class="card">
            <h2>Environment Information</h2>
            <pre>${JSON.stringify({
              timestamp: new Date().toISOString(),
              environment: process.env.NODE_ENV || 'not set',
              platform: process.platform,
              nodeVersion: process.version,
              dirname: __dirname,
              cwd: process.cwd(),
              frontendBuildPath: frontendBuildPath || 'Not found',
              indexPath: indexPath || 'Not found'
            }, null, 2)}</pre>
          </div>
        </body>
      </html>
    `);
  };
  
  // Fallback route for the root path - use the API status page
  app.get('/', renderApiStatusPage);
  
  // Fallback route for all other paths
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.redirect('/');
  });
  
  console.log('Fallback routes set up successfully');
}

// Add a root path handler for direct API server access
app.get('/api-status', (req, res) => {
  console.log('API status endpoint called at:', new Date().toISOString());
  res.send(`
    <html>
      <head>
        <title>Market Research Tool - API Status</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .card { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
          .success { color: green; }
          .warning { color: orange; }
          .error { color: red; }
          a { color: #0066cc; text-decoration: none; }
          a:hover { text-decoration: underline; }
        </style>
      </head>
      <body>
        <h1>Market Research Tool - API Status</h1>
        <div class="card">
          <h2>Server Status</h2>
          <p class="success">✅ API Server is running on port ${PORT}</p>
        </div>
        
        <div class="card">
          <h2>Available API Endpoints</h2>
          <ul>
            <li><strong>Gemini API:</strong> POST /api/gemini</li>
            <li><strong>OpenAI API:</strong> POST /api/openai</li>
            <li><strong>DeepSeek API:</strong> POST /api/deepseek</li>
            <li><strong>API Status:</strong> GET /api</li>
            <li><strong>Health Check:</strong> GET /api/healthcheck</li>
            <li><strong>Diagnostic:</strong> GET <a href="/api/diagnostic">/api/diagnostic</a></li>
          </ul>
        </div>
        
        <div class="card">
          <h2>Troubleshooting</h2>
          <p>If you're experiencing issues with the frontend, check the <a href="/api/diagnostic">diagnostic endpoint</a> for detailed information about the server environment.</p>
        </div>
      </body>
    </html>
  `);
});

// Catch-all route for frontend
app.get('*', (req, res) => {
  // Skip API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  
  console.log(`Serving index.html for path: ${req.path}`);
  
  if (indexPath && fs.existsSync(indexPath)) {
    res.sendFile(path.resolve(indexPath));
  } else {
    res.status(404).send('Frontend not found');
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API endpoints:`);
  console.log(`- Gemini: http://localhost:${PORT}/api/gemini`);
  console.log(`- OpenAI: http://localhost:${PORT}/api/openai`);
  console.log(`- DeepSeek: http://localhost:${PORT}/api/deepseek`);
  console.log(`- Diagnostic: http://localhost:${PORT}/api/diagnostic`);
  console.log(`- Status page: http://localhost:${PORT}/api`);
  
  if (fs.existsSync(frontendBuildPath)) {
    console.log(`Frontend: http://localhost:${PORT}/`);
  }
});

// Handle server shutdown gracefully
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});
